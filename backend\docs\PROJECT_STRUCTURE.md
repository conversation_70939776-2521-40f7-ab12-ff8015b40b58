# NestJS Backend Project Structure

## Overview
This document outlines the organization and file placement conventions for the NestJS backend application. The project follows NestJS best practices with a modular architecture organized by business domains.

## Root Structure
```
backend_nest/
├── src/                    # Source code directory
├── test/                   # Test configuration and e2e tests
├── migrations/             # Database migration files
├── package.json            # Dependencies and scripts
├── nest-cli.json          # NestJS CLI configuration
├── tsconfig.json          # TypeScript configuration
└── env.example            # Environment variables template
```

## Source Code Organization (`src/`)

### Core Application Files
- **`main.ts`** - Application entry point and bootstrap configuration
- **`app.module.ts`** - Root module that imports all feature modules
- **`app.controller.ts`** - Root controller for basic application endpoints
- **`app.service.ts`** - Root service for application-level business logic

### Feature Modules
Each business domain is organized into its own module directory following this structure:

```
src/
├── [domain-name]/
│   ├── [domain-name].entity.ts      # Database entity
│   ├── [domain-name].controller.ts  # HTTP endpoints
│   ├── [domain-name].service.ts     # Business logic
│   └── [domain-name].module.ts      # Module configuration
```

#### Available Feature Modules:
- **`users/`** - User management
- **`auth/`** - Authentication and authorization
- **`stores/`** - Store management
- **`products/`** - Product catalog
- **`customers/** - Customer management
- **`orders/`** - Order processing
- **`conversations/`** - Chat and messaging
- **`agents/`** - AI agent management
- **`images/`** - Image handling and storage
- **`tool-calls/`** - Tool execution tracking

### Authentication Module (`auth/`)
Contains authentication-related components with additional subdirectories:
- **`guards/`** - Authentication guards (JWT, Google, Local)
- **`strategies/`** - Passport authentication strategies
- **`middleware.ts`** - Custom authentication middleware

### Shared Resources
- **`shared/`** - Common enums and shared constants
- **`types/`** - TypeScript type definitions and interfaces
- **`utils/`** - Utility functions and helper classes

### Database
- **`migrations/`** - Database schema migration files
- **Entities** - Distributed across feature modules

## File Naming Conventions

### Controllers
- **Purpose**: Handle HTTP requests and responses
- **Naming**: `[domain-name].controller.ts`
- **Location**: Within respective feature module directories
- **Responsibilities**: Request validation, response formatting, route handling

### Services
- **Purpose**: Contain business logic and data operations
- **Naming**: `[domain-name].service.ts`
- **Location**: Within respective feature module directories
- **Responsibilities**: Database operations, business rules, external API calls

### Entities
- **Purpose**: Define database table structures and relationships
- **Naming**: `[domain-name].entity.ts`
- **Location**: Within respective feature module directories
- **Responsibilities**: Data model definition, validation decorators, relationships

### Modules
- **Purpose**: Configure dependency injection and module imports
- **Naming**: `[domain-name].module.ts`
- **Location**: Within respective feature module directories
- **Responsibilities**: Module configuration, provider registration, imports/exports

## Architecture Patterns

### Module Structure
Each feature module follows the same pattern:
1. **Entity** - Data model definition
2. **Service** - Business logic implementation
3. **Controller** - HTTP endpoint handling
4. **Module** - Dependency injection configuration

### Dependency Injection
- Services are injected into controllers
- Repositories are injected into services
- Guards and strategies are injected into modules
- Configuration is injected globally where appropriate

### Database Layer
- Uses TypeORM for database operations
- Entities are distributed across feature modules
- Migrations are centralized in the migrations directory
- Database configuration is centralized in the root module

## Configuration and Environment
- Environment variables are loaded through ConfigModule
- Database configuration is centralized in app.module.ts
- JWT and authentication settings are configured at the application level
- TypeORM configuration includes connection pooling and SSL settings

## Testing Structure
- Unit tests are co-located with source files (`.spec.ts`)
- E2E tests are located in the `test/` directory
- Jest configuration is defined in package.json
- Test environment is configured for Node.js

## Utilities and Helpers
- **API Response** - Standardized response formatting
- **BigInt Handling** - Database bigint type management
- **Serialization** - Data transformation utilities
- **Logging** - Application logging configuration
- **Currency** - Financial calculation helpers
- **UUID** - Unique identifier generation

## Migration and Database Management
- Database migrations are versioned with timestamps
- Migration scripts are available through npm scripts
- Database seeding and rollback capabilities
- Connection pooling and timeout configurations

This structure promotes maintainability, scalability, and follows NestJS architectural best practices while keeping related functionality organized within cohesive modules.
