"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.buildConversationTools = buildConversationTools;
const dist_1 = require("../../../../../ai-agent/dist");
const db_1 = require("../../db");
const _1 = require(".");
function buildConversationTools(db, conversationUuid) {
    console.log(`🔧 buildConversationTools called with:`, {
        dbType: typeof db,
        dbInitialized: db?.isInitialized,
        conversationUuid,
        timestamp: new Date().toISOString()
    });
    if (!db || !db.isInitialized) {
        console.error(`❌ Database connection validation failed:`, {
            dbExists: !!db,
            dbInitialized: db?.isInitialized,
            dbType: typeof db
        });
        throw new Error('Database connection is not properly initialized');
    }
    console.log(`✅ Database connection validated successfully`);
    const placeOrderTool = async (params) => {
        console.log(`🔧 placeOrderTool called with:`, {
            params,
            conversationUuid,
            timestamp: new Date().toISOString()
        });
        let freshDb;
        try {
            freshDb = await (0, db_1.getDatabase)();
            console.log(`✅ Fresh database connection obtained:`, {
                dbType: typeof freshDb,
                dbInitialized: freshDb?.isInitialized
            });
        }
        catch (error) {
            console.error(`❌ Failed to get fresh database connection:`, error);
            throw new Error('Failed to establish database connection for tool execution');
        }
        if (!freshDb || !freshDb.isInitialized) {
            console.error(`❌ Fresh database connection validation failed:`, {
                dbExists: !!freshDb,
                dbInitialized: freshDb?.isInitialized,
                dbType: typeof freshDb
            });
            throw new Error('Fresh database connection is not properly initialized');
        }
        console.log(`✅ Fresh database connection validated successfully`);
        return (0, _1.placeOrderTool)(params, freshDb, conversationUuid);
    };
    const updateOrderTool = async (params) => {
        console.log(`🔧 updateOrderTool called with:`, {
            params,
            conversationUuid,
            timestamp: new Date().toISOString()
        });
        let freshDb;
        try {
            freshDb = await (0, db_1.getDatabase)();
            console.log(`✅ Fresh database connection obtained for updateOrder:`, {
                dbType: typeof freshDb,
                dbInitialized: freshDb?.isInitialized
            });
        }
        catch (error) {
            console.error(`❌ Failed to get fresh database connection for updateOrder:`, error);
            throw new Error('Failed to establish database connection for tool execution');
        }
        if (!freshDb || !freshDb.isInitialized) {
            console.error(`❌ Fresh database connection validation failed for updateOrder:`, {
                dbExists: !!freshDb,
                dbInitialized: freshDb?.isInitialized,
                dbType: typeof freshDb
            });
            throw new Error('Fresh database connection is not properly initialized');
        }
        console.log(`✅ Fresh database connection validated successfully for updateOrder`);
        return (0, _1.updateOrderTool)(params, freshDb, conversationUuid);
    };
    const placeOrder = (0, dist_1.createTool)(placeOrderTool, {
        name: 'placeOrder',
        description: 'Place a new order. Provide customerName, customerPhone, customerAddress, and items. Each item needs quantity and productId (numeric ID) for proper identification. Optionally include customerEmail, useTax, taxRate, and priority. ',
        parameterTypes: {
            customerName: { type: 'string' },
            customerPhone: { type: 'string' },
            customerAddress: { type: 'string' },
            customerEmail: { type: 'string', optional: true },
            items: {
                type: 'array',
                items: {
                    type: 'object',
                    properties: {
                        productId: { type: 'number' },
                        productName: { type: 'string', optional: true },
                        quantity: { type: 'number' },
                        taxAmount: { type: 'number', optional: true },
                    },
                    required: ['productId', 'quantity'],
                },
            },
            useTax: { type: 'boolean', optional: true },
            taxRate: { type: 'number', optional: true },
            priority: { type: 'string', optional: true },
        },
        requiredParams: ['customerName', 'customerPhone', 'customerAddress', 'items'],
    });
    const updateOrder = (0, dist_1.createTool)(updateOrderTool, {
        name: 'updateOrder',
        description: 'Update an existing order with delta-like behavior. Provide orderNumber and any combination of: customerName, customerEmail, customerPhone, useTax, taxRate, priority, status, expectedDeliveryDate, preferredDeliveryLocation. For items, use itemsToAdd to add new items (quantities merge with existing by default, or set replaceQuantities=true to replace), itemsToRemove to remove specific quantities, or removeAllItems to clear all items. Only works with orders in DRAFT, PENDING, CONFIRMED, or PROCESSING status (orders that have not been shipped yet). For items, prefer using productName over productId for better reliability.',
        parameterTypes: {
            orderNumber: { type: 'string' },
            customerName: { type: 'string', optional: true },
            customerEmail: { type: 'string', optional: true },
            customerPhone: { type: 'string', optional: true },
            itemsToAdd: {
                type: 'array',
                optional: true,
                items: {
                    type: 'object',
                    properties: {
                        productId: { type: 'string', optional: true },
                        productName: { type: 'string', optional: true },
                        quantity: { type: 'number' },
                        taxAmount: { type: 'number', optional: true },
                    },
                    required: ['quantity'],
                },
            },
            itemsToRemove: {
                type: 'array',
                optional: true,
                items: {
                    type: 'object',
                    properties: {
                        productId: { type: 'string', optional: true },
                        productName: { type: 'string', optional: true },
                        quantity: { type: 'number', optional: true },
                    },
                    required: [],
                },
            },
            removeAllItems: { type: 'boolean', optional: true },
            replaceQuantities: { type: 'boolean', optional: true },
            useTax: { type: 'boolean', optional: true },
            taxRate: { type: 'number', optional: true },
            priority: { type: 'string', optional: true },
            status: { type: 'string', optional: true },
            expectedDeliveryDate: { type: 'string', optional: true },
            preferredDeliveryLocation: { type: 'string', optional: true },
        },
        requiredParams: ['orderNumber'],
    });
    const toolsArray = [placeOrder, updateOrder];
    return toolsArray;
}
//# sourceMappingURL=agent-conversation-tools.js.map