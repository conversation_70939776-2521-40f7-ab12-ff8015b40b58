import { Module } from '@nestjs/common';
import { ConfigModule } from '@nestjs/config';
import { TypeOrmModule } from '@nestjs/typeorm';
import { PassportModule } from '@nestjs/passport';
import { JwtModule } from '@nestjs/jwt';

// Import modules
import { AuthModule } from './auth/auth.module';
import { UsersModule } from './users/users.module';
import { StoresModule } from './stores/stores.module';
import { ProductsModule } from './products/products.module';
import { CustomersModule } from './customers/customers.module';
import { OrdersModule } from './orders/orders.module';
import { ConversationsModule } from './conversations/conversations.module';
import { AgentsModule } from './agents/agents.module';
import { ImagesModule } from './images/images.module';
import { ToolCallsModule } from './tool-calls/tool-calls.module';

// Import entities from their respective modules
import { User } from './users/user.entity';
import { Store } from './stores/store.entity';
import { Customer } from './customers/customer.entity';
import { Product } from './products/product.entity';
import { Order } from './orders/order.entity';
import { OrderItem } from './orders/order-item.entity';
import { Conversation } from './conversations/conversation.entity';
import { Message } from './conversations/message.entity';
import { ToolCall } from './tool-calls/tool-call.entity';
import { Agent } from './agents/agent.entity';
import { Account } from './auth/account.entity';
import { Session } from './auth/session.entity';
import { VerificationToken } from './auth/verification-token.entity';
import { Post } from './auth/post.entity';

// Import app components
import { AppController } from './app.controller';
import { AppService } from './app.service';

@Module({
  imports: [
    // Configuration
    ConfigModule.forRoot({
      isGlobal: true,
      envFilePath: '.env',
    }),

    // Database
    TypeOrmModule.forRoot({
      type: 'postgres',
      host: process.env.DB_HOST || 'localhost',
      port: parseInt(process.env.DB_PORT || '5432'),
      username: process.env.DB_USER || 'postgres',
      password: process.env.DB_PASSWORD || '',
      database: process.env.DB_NAME || 'teno_store',
      synchronize: false,
      logging: false,
      entities: [
        User,
        Store,
        Customer,
        Product,
        Order,
        OrderItem,
        Conversation,
        Message,
        ToolCall,
        Agent,
        Account,
        Session,
        VerificationToken,
        Post
      ],
      // Migrations are handled by CLI scripts; avoid importing TS at runtime
      // migrations: ['dist/migrations/*.js'],
      ssl: process.env.NODE_ENV === 'production' ? { rejectUnauthorized: false } : false,
      extra: {
        max: 20,
        connectionTimeoutMillis: 10000,
        idleTimeoutMillis: 30000,
      },
    }),

    // Authentication
    PassportModule.register({ defaultStrategy: 'jwt' }),
    JwtModule.register({
      secret: process.env.JWT_SECRET || 'your-secret-key',
      signOptions: { expiresIn: '24h' },
    }),

    // Feature modules
    AuthModule,
    UsersModule,
    StoresModule,
    ProductsModule,
    CustomersModule,
    OrdersModule,
    ConversationsModule,
    AgentsModule,
    ImagesModule,
    ToolCallsModule,
  ],
  controllers: [AppController],
  providers: [AppService],
})
export class AppModule {}
