export declare function checkOrderUnderwayTool(params: {
    customerEmail?: string;
    customerName?: string;
    items?: Array<{
        productId?: string | number | bigint;
        productName?: string;
        quantity: number;
        taxAmount?: number;
    }>;
}, db: any, conversationUuid: string): Promise<{
    readonly underway: false;
    readonly message: "No existing customer found for the provided email.";
    orderId?: undefined;
    orderNumber?: undefined;
    status?: undefined;
    total?: undefined;
} | {
    readonly underway: false;
    readonly message: "No existing customer found for the provided name.";
    orderId?: undefined;
    orderNumber?: undefined;
    status?: undefined;
    total?: undefined;
} | {
    readonly underway: false;
    readonly message: "No order currently underway.";
    orderId?: undefined;
    orderNumber?: undefined;
    status?: undefined;
    total?: undefined;
} | {
    underway: boolean;
    orderId: any;
    orderNumber: any;
    status: any;
    total: any;
    message: string;
}>;
export declare const checkOrderUnderway: import("../../../../../ai-agent/dist").ToolFunction;
