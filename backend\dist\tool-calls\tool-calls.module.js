"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.ToolCallsModule = void 0;
const common_1 = require("@nestjs/common");
const typeorm_1 = require("@nestjs/typeorm");
const tool_calls_controller_1 = require("./tool-calls.controller");
const tool_calls_service_1 = require("./tool-calls.service");
const tool_call_entity_1 = require("./tool-call.entity");
let ToolCallsModule = class ToolCallsModule {
};
exports.ToolCallsModule = ToolCallsModule;
exports.ToolCallsModule = ToolCallsModule = __decorate([
    (0, common_1.Module)({
        imports: [typeorm_1.TypeOrmModule.forFeature([tool_call_entity_1.ToolCall])],
        controllers: [tool_calls_controller_1.ToolCallsController],
        providers: [tool_calls_service_1.ToolCallsService],
        exports: [tool_calls_service_1.ToolCallsService],
    })
], ToolCallsModule);
//# sourceMappingURL=tool-calls.module.js.map