import { OrderStatusService } from '../../services/order.service';
export declare const UNDERWAY_STATUSES: Array<OrderStatusService>;
export declare function canConvertToBigInt(value: unknown): boolean;
export declare function resolveItemsAgainstCatalog(items: Array<{
    productId?: string | number | bigint;
    productName?: string;
    quantity: number;
    taxAmount?: number;
}>, storeId: bigint, db: any): Promise<{
    productId: bigint;
    productName: any;
    quantity: number;
    unitPrice: number;
    lineTotal: number;
    taxAmount: number;
}[]>;
