"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.getDatabase = getDatabase;
exports.closeDatabase = closeDatabase;
const typeorm_1 = require("typeorm");
const user_entity_1 = require("../../users/user.entity");
const store_entity_1 = require("../../stores/store.entity");
const customer_entity_1 = require("../../customers/customer.entity");
const product_entity_1 = require("../../products/product.entity");
const order_entity_1 = require("../../orders/order.entity");
const order_item_entity_1 = require("../../orders/order-item.entity");
const conversation_entity_1 = require("../../conversations/conversation.entity");
const message_entity_1 = require("../../conversations/message.entity");
const tool_call_entity_1 = require("../../tool-calls/tool-call.entity");
const agent_entity_1 = require("../agent.entity");
const account_entity_1 = require("../../auth/account.entity");
const session_entity_1 = require("../../auth/session.entity");
const verification_token_entity_1 = require("../../auth/verification-token.entity");
let dataSource = null;
async function getDatabase() {
    if (dataSource && dataSource.isInitialized) {
        return dataSource;
    }
    dataSource = new typeorm_1.DataSource({
        type: 'postgres',
        host: process.env.DB_HOST || 'localhost',
        port: parseInt(process.env.DB_PORT || '5432'),
        username: process.env.DB_USER || 'postgres',
        password: process.env.DB_PASSWORD || '',
        database: process.env.DB_NAME || 'teno_store',
        synchronize: false,
        logging: false,
        entities: [
            user_entity_1.User,
            store_entity_1.Store,
            customer_entity_1.Customer,
            product_entity_1.Product,
            order_entity_1.Order,
            order_item_entity_1.OrderItem,
            conversation_entity_1.Conversation,
            message_entity_1.Message,
            tool_call_entity_1.ToolCall,
            agent_entity_1.Agent,
            account_entity_1.Account,
            session_entity_1.Session,
            verification_token_entity_1.VerificationToken,
        ],
        ssl: process.env.NODE_ENV === 'production' ? { rejectUnauthorized: false } : false,
        extra: {
            max: 20,
            connectionTimeoutMillis: 10000,
            idleTimeoutMillis: 30000,
        },
    });
    if (!dataSource.isInitialized) {
        await dataSource.initialize();
    }
    return dataSource;
}
async function closeDatabase() {
    if (dataSource && dataSource.isInitialized) {
        await dataSource.destroy();
        dataSource = null;
    }
}
//# sourceMappingURL=index.js.map