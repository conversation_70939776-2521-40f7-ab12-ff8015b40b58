import { DataSource } from 'typeorm';
import { Customer } from '../../customers/customer.entity';
export interface CustomerFilter {
    email?: string;
    name?: string;
    phone?: string;
    storeId?: string | bigint;
}
export declare function filterCustomers(filter: CustomerFilter, db: DataSource): Promise<Customer[]>;
export declare function createCustomer(customerData: Partial<Customer> & {
    storeId: string | bigint;
    createdBy?: string | bigint;
}, db: DataSource): Promise<Customer>;
export declare function updateCustomer(id: string, updateData: Partial<Customer>, db: DataSource): Promise<Customer>;
