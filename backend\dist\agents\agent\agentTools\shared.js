"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.UNDERWAY_STATUSES = void 0;
exports.canConvertToBigInt = canConvertToBigInt;
exports.resolveItemsAgainstCatalog = resolveItemsAgainstCatalog;
const order_service_1 = require("../../services/order.service");
exports.UNDERWAY_STATUSES = [
    order_service_1.OrderStatusService.DRAFT,
    order_service_1.OrderStatusService.PENDING,
    order_service_1.OrderStatusService.CONFIRMED,
    order_service_1.OrderStatusService.PROCESSING,
];
function canConvertToBigInt(value) {
    if (typeof value === 'bigint')
        return true;
    if (typeof value === 'number')
        return Number.isInteger(value) && value >= 0;
    if (typeof value === 'string')
        return /^\d+$/.test(value.trim());
    return false;
}
async function resolveItemsAgainstCatalog(items, storeId, db) {
    const idCandidates = items
        .map((it) => it.productId)
        .filter((v) => typeof v !== 'undefined' && canConvertToBigInt(v))
        .map((v) => BigInt(v));
    const nameCandidates = items
        .map((it) => (typeof it.productName === 'string' && it.productName.trim().length > 0
        ? it.productName.trim()
        : (typeof it.productId === 'string' && !canConvertToBigInt(it.productId)
            ? String(it.productId).trim()
            : undefined)))
        .filter((v) => typeof v === 'string' && v.length > 0);
    const productRepo = db.getRepository('Product');
    const allStoreProducts = await productRepo.find({
        where: {
            isDeleted: false,
            storeId,
        },
        select: { id: true, name: true, price: true },
    });
    const productById = new Map(allStoreProducts.map((p) => [p.id.toString(), p]));
    const productByName = new Map(allStoreProducts.map((p) => [p.name.toLowerCase(), p]));
    const products = [];
    if (idCandidates.length > 0) {
        const idMatches = allStoreProducts.filter(p => idCandidates.includes(p.id));
        products.push(...idMatches);
    }
    if (nameCandidates.length > 0) {
        for (const searchName of nameCandidates) {
            const trimmedSearch = searchName.trim();
            let found = false;
            for (const product of allStoreProducts) {
                if (product.name.toLowerCase() === trimmedSearch.toLowerCase()) {
                    products.push(product);
                    found = true;
                    break;
                }
            }
            if (!found) {
                for (const product of allStoreProducts) {
                    const productName = product.name.toLowerCase();
                    const searchTerm = trimmedSearch.toLowerCase();
                    if (productName.includes(searchTerm) || searchTerm.includes(productName)) {
                        products.push(product);
                        break;
                    }
                }
            }
        }
    }
    const availableProducts = await productRepo.find({
        where: {
            isDeleted: false,
            storeId,
        },
        select: { id: true, name: true, price: true },
        take: 10
    });
    const preparedItems = items.map((it, index) => {
        let resolved;
        let searchTerm = '';
        if (typeof it.productId !== 'undefined' && canConvertToBigInt(it.productId)) {
            resolved = productById.get(BigInt(it.productId).toString());
            searchTerm = `ID: ${it.productId}`;
        }
        else if (typeof it.productName === 'string' && it.productName.trim().length > 0) {
            const searchName = it.productName.trim().toLowerCase();
            searchTerm = `Name: "${it.productName}"`;
            resolved = productByName.get(searchName);
            if (!resolved) {
                for (const [productName, product] of Array.from(productByName.entries())) {
                    if (productName.includes(searchName) || searchName.includes(productName)) {
                        resolved = product;
                        break;
                    }
                }
            }
        }
        else if (typeof it.productId === 'string') {
            const searchName = it.productId.trim().toLowerCase();
            searchTerm = `Name: "${it.productId}"`;
            resolved = productByName.get(searchName);
        }
        if (!resolved) {
            const itemInfo = [];
            if (it.productId !== undefined)
                itemInfo.push(`productId: ${it.productId} (type: ${typeof it.productId})`);
            if (it.productName !== undefined)
                itemInfo.push(`productName: "${it.productName}" (type: ${typeof it.productName})`);
            const availableProductsInfo = availableProducts.map((p) => `"${p.name}" (ID: ${p.id})`).join(', ');
            const errorMsg = `Product not found for item ${index + 1}. Searched for: [${itemInfo.join(', ')}].\n` +
                `Available products in store: ${availableProductsInfo}${availableProducts.length >= 10 ? ' (showing first 10)' : ''}`;
            throw new Error(errorMsg);
        }
        const unitPrice = Number(resolved.price);
        const lineTotal = unitPrice * it.quantity + (it.taxAmount ?? 0);
        return {
            productId: resolved.id,
            productName: resolved.name,
            quantity: it.quantity,
            unitPrice,
            lineTotal,
            taxAmount: it.taxAmount ?? 0,
        };
    });
    return preparedItems;
}
//# sourceMappingURL=shared.js.map