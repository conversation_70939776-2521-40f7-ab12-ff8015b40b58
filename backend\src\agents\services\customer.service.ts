import { DataSource } from 'typeorm';
import { Customer } from '../../customers/customer.entity';

export interface CustomerFilter {
  email?: string;
  name?: string;
  phone?: string;
  storeId?: string | bigint;
}

export async function filterCustomers(
  filter: CustomerFilter,
  db: DataSource
): Promise<Customer[]> {
  const customerRepository = db.getRepository(Customer);
  const where: any = { isDeleted: false };

  if (filter.storeId) {
    where.storeId = filter.storeId;
  }

  if (filter.email) {
    where.email = filter.email;
  }

  if (filter.name) {
    // Search by name (case-insensitive)
    where.name = filter.name;
  }

  if (filter.phone) {
    where.phone = filter.phone;
  }

  return customerRepository.find({
    where,
    order: { createdAt: 'DESC' },
  });
}

export async function createCustomer(
  customerData: Partial<Customer> & { 
    storeId: string | bigint;
    createdBy?: string | bigint;
  },
  db: DataSource
): Promise<Customer> {
  const customerRepository = db.getRepository(Customer);
  
  const customer = customerRepository.create({
    ...customerData,
    storeId: customerData.storeId.toString(),
    createdBy: customerData.createdBy?.toString(),
  });

  return customerRepository.save(customer);
}

export async function updateCustomer(
  id: string,
  updateData: Partial<Customer>,
  db: DataSource
): Promise<Customer> {
  const customerRepository = db.getRepository(Customer);
  
  const customer = await customerRepository.findOne({
    where: { id, isDeleted: false },
  });

  if (!customer) {
    throw new Error(`Customer with ID ${id} not found`);
  }

  Object.assign(customer, updateData);
  return customerRepository.save(customer);
}
