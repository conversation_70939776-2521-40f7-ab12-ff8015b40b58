import { Entity, PrimaryGeneratedColumn, Column, CreateDateColumn, UpdateDateColumn, OneToMany, ManyToOne } from 'typeorm';

@Entity('conversations')
export class Conversation {
  @PrimaryGeneratedColumn('increment', { type: 'bigint' })
  id: string;

  @Column({ type: 'varchar', unique: true })
  uuid: string;

  @Column({ type: 'varchar' })
  title: string;

  @Column({ type: 'text', nullable: true })
  description: string;

  @Column({ type: 'varchar', nullable: true })
  status: string;

  @Column({ type: 'boolean', default: false })
  isDeleted: boolean;

  @CreateDateColumn()
  createdAt: Date;

  @UpdateDateColumn()
  updatedAt: Date;

  @Column({ type: 'bigint' })
  createdBy: string;

  @Column({ nullable: true, type: 'bigint' })
  updatedBy: string;

  @Column({ type: 'bigint' })
  userId: string;

  @Column({ type: 'bigint' })
  storeId: string;

  @Column({ type: 'bigint', nullable: true })
  agentId: string;

  // Relations - using string literals to avoid circular dependency
  @ManyToOne('User', (user: any) => user.conversations)
  user: any;

  @ManyToOne('Store', (store: any) => store.conversations)
  store: any;

  @ManyToOne('Agent', (agent: any) => agent.conversations)
  agent: any;

  @OneToMany('Message', (message: any) => message.conversation)
  messages: any[];
}
