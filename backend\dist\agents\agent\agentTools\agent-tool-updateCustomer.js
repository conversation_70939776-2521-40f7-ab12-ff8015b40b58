"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.updateCustomerTool = updateCustomerTool;
async function updateCustomerTool(params, db, conversationUuid) {
    const { customerId, phone, address, email, name } = params || {};
    if (!customerId) {
        throw new Error('Customer ID is required');
    }
    if (!db || !db.isInitialized) {
        throw new Error('Database connection is not properly initialized');
    }
    const conversationRepo = db.getRepository('Conversation');
    const customerRepo = db.getRepository('Customer');
    const convo = await conversationRepo.findOne({
        where: { uuid: conversationUuid, isDeleted: false },
        select: ['id', 'userId', 'storeId', 'createdBy', 'context'],
    });
    if (!convo)
        throw new Error('Conversation not found');
    const existingCustomer = await customerRepo.findOne({
        where: { id: customerId, storeId: convo.storeId, isDeleted: false },
    });
    if (!existingCustomer)
        throw new Error('Customer not found');
    const updateData = {};
    if (phone !== undefined)
        updateData.phone = phone;
    if (address !== undefined)
        updateData.address = address;
    if (email !== undefined)
        updateData.email = email;
    if (name !== undefined)
        updateData.name = name;
    if (Object.keys(updateData).length === 0) {
        return {
            message: 'No fields to update',
            customer: existingCustomer
        };
    }
    const updatedCustomer = await customerRepo.update({ id: customerId }, {
        ...updateData,
        updatedBy: convo.createdBy,
        updatedAt: new Date(),
    });
    const currentCtx = await conversationRepo.findOne({
        where: { id: convo.id },
        select: ['context'],
    });
    if (currentCtx && currentCtx.context) {
        const updatedContext = {
            ...currentCtx.context,
            customer: {
                id: customerId.toString(),
                name: updatedCustomer.name,
                email: updatedCustomer.email,
                phone: updatedCustomer.phone,
                address: updatedCustomer.address,
            },
        };
        await conversationRepo.update({ id: convo.id }, { context: updatedContext });
    }
    return {
        message: 'Customer updated successfully',
        customer: updatedCustomer,
        updatedFields: Object.keys(updateData)
    };
}
//# sourceMappingURL=agent-tool-updateCustomer.js.map