"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.filterCustomers = filterCustomers;
exports.createCustomer = createCustomer;
exports.updateCustomer = updateCustomer;
const customer_entity_1 = require("../../customers/customer.entity");
async function filterCustomers(filter, db) {
    const customerRepository = db.getRepository(customer_entity_1.Customer);
    const where = { isDeleted: false };
    if (filter.storeId) {
        where.storeId = filter.storeId;
    }
    if (filter.email) {
        where.email = filter.email;
    }
    if (filter.name) {
        where.name = filter.name;
    }
    if (filter.phone) {
        where.phone = filter.phone;
    }
    return customerRepository.find({
        where,
        order: { createdAt: 'DESC' },
    });
}
async function createCustomer(customerData, db) {
    const customerRepository = db.getRepository(customer_entity_1.Customer);
    const customer = customerRepository.create({
        ...customerData,
        storeId: customerData.storeId.toString(),
        createdBy: customerData.createdBy?.toString(),
    });
    return customerRepository.save(customer);
}
async function updateCustomer(id, updateData, db) {
    const customerRepository = db.getRepository(customer_entity_1.Customer);
    const customer = await customerRepository.findOne({
        where: { id, isDeleted: false },
    });
    if (!customer) {
        throw new Error(`Customer with ID ${id} not found`);
    }
    Object.assign(customer, updateData);
    return customerRepository.save(customer);
}
//# sourceMappingURL=customer.service.js.map