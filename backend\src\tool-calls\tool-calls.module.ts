import { Modu<PERSON> } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { ToolCallsController } from './tool-calls.controller';
import { ToolCallsService } from './tool-calls.service';
import { ToolCall } from './tool-call.entity';

@Module({
  imports: [TypeOrmModule.forFeature([ToolCall])],
  controllers: [ToolCallsController],
  providers: [ToolCallsService],
  exports: [ToolCallsService],
})
export class ToolCallsModule {}
