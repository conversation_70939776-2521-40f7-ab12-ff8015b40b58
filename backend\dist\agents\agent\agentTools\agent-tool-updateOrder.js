"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.updateOrderTool = updateOrderTool;
const order_service_1 = require("../../services/order.service");
const order_service_2 = require("../../services/order.service");
const shared_1 = require("./shared");
async function updateOrderTool(params, db, conversationUuid) {
    if (!db || !db.isInitialized) {
        throw new Error('Database connection is not properly initialized');
    }
    const conversationRepo = db.getRepository('Conversation');
    const orderRepo = db.getRepository('Order');
    const convo = await conversationRepo.findOne({
        where: { uuid: conversationUuid, isDeleted: false },
        select: ['id', 'userId', 'storeId', 'createdBy', 'context'],
    });
    if (!convo)
        throw new Error('Conversation not found');
    const existingOrder = await orderRepo.findOne({
        where: { orderNumber: params.orderNumber, storeId: convo.storeId, isDeleted: false },
    });
    if (!existingOrder) {
        throw new Error(`Order ${params.orderNumber} not found or does not belong to this store`);
    }
    const modifiableStatuses = ['draft', 'pending', 'confirmed', 'processing'];
    if (!modifiableStatuses.includes(existingOrder.status?.toLowerCase())) {
        throw new Error(`Cannot modify order ${params.orderNumber} in status ${existingOrder.status}`);
    }
    let finalItems = [];
    if (params.removeAllItems) {
        finalItems = [];
    }
    else {
        finalItems = existingOrder.items.map((item) => ({
            productId: item.productId,
            productName: item.productName,
            quantity: Number(item.quantity),
            unitPrice: item.unitPrice,
            taxAmount: item.taxAmount,
        }));
    }
    if (!params.removeAllItems && params.itemsToRemove && params.itemsToRemove.length > 0) {
        const removeMap = new Map();
        params.itemsToRemove.forEach((item) => {
            const key = item.productId?.toString();
            if (key) {
                const existing = removeMap.get(key) || 0;
                removeMap.set(key, existing + (item.quantity || 0));
            }
        });
        finalItems = finalItems.filter((item) => {
            const key = item.productId?.toString();
            const removeQuantity = removeMap.get(key) || 0;
            if (removeQuantity === 0) {
                return true;
            }
            if (removeQuantity >= item.quantity) {
                return false;
            }
            item.quantity = item.quantity - removeQuantity;
            return item.quantity > 0;
        });
    }
    if (params.itemsToAdd && params.itemsToAdd.length > 0) {
        let newItems = [];
        try {
            for (let i = 0; i < params.itemsToAdd.length; i++) {
                const item = params.itemsToAdd[i];
                if (!item.productId && !item.productName) {
                    throw new Error(`Item ${i + 1}: Either productId or productName is required for proper identification`);
                }
            }
            const resolved = await (0, shared_1.resolveItemsAgainstCatalog)(params.itemsToAdd, convo.storeId, db);
            newItems = resolved.map((it) => ({
                productId: it.productId,
                productName: it.productName,
                quantity: it.quantity,
                unitPrice: it.unitPrice,
                taxAmount: it.taxAmount,
            }));
            const itemMap = new Map();
            finalItems.forEach((item) => {
                if (item.productId) {
                    itemMap.set(item.productId.toString(), { ...item });
                }
            });
            newItems.forEach((item) => {
                const productIdKey = item.productId.toString();
                if (itemMap.has(productIdKey)) {
                    const existing = itemMap.get(productIdKey);
                    const oldQuantity = existing.quantity;
                    const newQuantity = params.replaceQuantities
                        ? item.quantity
                        : existing.quantity + item.quantity;
                    console.log(`[updateOrderTool] Merging item ${item.productName} (ID: ${productIdKey}): ${oldQuantity} + ${item.quantity} = ${newQuantity} (replaceQuantities: ${params.replaceQuantities})`);
                    itemMap.set(productIdKey, {
                        ...existing,
                        quantity: newQuantity,
                        unitPrice: item.unitPrice,
                        taxAmount: item.taxAmount,
                    });
                }
                else {
                    console.log(`[updateOrderTool] Adding new item ${item.productName} (ID: ${productIdKey}): quantity ${item.quantity}`);
                    itemMap.set(productIdKey, { ...item });
                }
            });
            finalItems = Array.from(itemMap.values());
        }
        catch (error) {
            throw new Error(`Failed to resolve items to add: ${error}`);
        }
    }
    finalItems = finalItems.filter((item) => item.quantity > 0);
    const updateData = {
        id: existingOrder.id,
        updatedBy: convo.createdBy,
    };
    if (params.customerName !== undefined)
        updateData.customerName = params.customerName;
    if (params.customerEmail !== undefined)
        updateData.customerEmail = params.customerEmail;
    if (params.customerPhone !== undefined)
        updateData.customerPhone = params.customerPhone;
    if (params.useTax !== undefined)
        updateData.useTax = params.useTax;
    if (params.taxRate !== undefined)
        updateData.taxRate = params.taxRate;
    if (params.expectedDeliveryDate !== undefined)
        updateData.expectedDeliveryDate = new Date(params.expectedDeliveryDate);
    if (params.preferredDeliveryLocation !== undefined)
        updateData.preferredDeliveryLocation = params.preferredDeliveryLocation;
    if (params.priority) {
        const priorityKey = params.priority.toUpperCase();
        if (priorityKey in order_service_2.OrderPriorityService) {
            updateData.priority = order_service_2.OrderPriorityService[priorityKey];
        }
    }
    if (params.status) {
        const statusKey = params.status.toUpperCase();
        if (statusKey in order_service_2.OrderStatusService) {
            updateData.status = order_service_2.OrderStatusService[statusKey];
        }
    }
    updateData.items = finalItems;
    const updatedOrder = await (0, order_service_1.updateOrder)(db, updateData);
    const currentCtx = await conversationRepo.findOne({
        where: { id: convo.id },
        select: ['context'],
    });
    if (currentCtx && currentCtx.context) {
        const updatedContext = {
            ...currentCtx.context,
            order: {
                id: existingOrder.id.toString(),
                orderNumber: existingOrder.orderNumber,
                status: existingOrder.status,
                priority: existingOrder.priority,
                total: existingOrder.total,
                currency: existingOrder.currency,
                updatedAt: existingOrder.updatedAt,
            },
        };
        await conversationRepo.update({ id: convo.id }, { context: updatedContext });
    }
    let summaryMessage = `Order ${updatedOrder.orderNumber} updated successfully. `;
    if (params.removeAllItems) {
        summaryMessage += `All items removed. `;
    }
    else if (params.itemsToRemove && params.itemsToRemove.length > 0) {
        summaryMessage += `Items removed. `;
    }
    if (params.itemsToAdd && params.itemsToAdd.length > 0) {
        const behavior = params.replaceQuantities ? 'replaced' : 'added to';
        summaryMessage += `Items ${behavior} existing quantities. `;
    }
    summaryMessage += `New total: ${updatedOrder.total} ${updatedOrder.currency || 'USD'}`;
    return {
        message: summaryMessage,
        orderId: updatedOrder.id,
        orderNumber: updatedOrder.orderNumber,
        total: updatedOrder.total,
        status: updatedOrder.status,
        items: [],
        itemsAdded: params.itemsToAdd?.length || 0,
        itemsRemoved: params.removeAllItems ? 'all' : (params.itemsToRemove?.length || 0),
        totalItems: 0,
    };
}
//# sourceMappingURL=agent-tool-updateOrder.js.map