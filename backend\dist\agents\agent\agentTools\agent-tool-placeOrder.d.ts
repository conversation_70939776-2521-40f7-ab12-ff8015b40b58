export declare function placeOrderTool(params: {
    customerName: string;
    customerEmail?: string;
    customerPhone: string;
    customerAddress: string;
    items: Array<{
        productId?: string | number | bigint;
        productName?: string;
        quantity: number;
        taxAmount?: number;
    }>;
    useTax?: boolean;
    taxRate?: number;
    priority?: 'low' | 'normal' | 'high' | 'urgent';
}, db: any, conversationUuid: string): Promise<{
    message: string;
    orderNumber: string;
    orderId: string;
    underway: boolean;
    total: number;
    customerName: any;
    customerPhone: any;
    status: import("../../../orders/order.entity").OrderStatus;
    priority: import("../../../orders/order.entity").OrderPriority;
    items: any[];
    trackingUrl: string;
} | {
    message: string;
    orderNumber: any;
    orderId: string;
    total: number;
    trackingUrl: string;
    underway?: undefined;
    customerName?: undefined;
    customerPhone?: undefined;
    status?: undefined;
    priority?: undefined;
    items?: undefined;
}>;
export declare const placeOrder: import("../../../../../ai-agent/dist").ToolFunction;
