import { DataSource } from 'typeorm';
import { Order } from '../../orders/order.entity';
import { OrderItem } from '../../orders/order-item.entity';
import { Customer } from '../../customers/customer.entity';

export enum OrderStatusService {
  DRAFT = 'draft',
  PENDING = 'pending',
  CONFIRMED = 'confirmed',
  PROCESSING = 'processing',
  SHIPPED = 'shipped',
  DELIVERED = 'delivered',
  CANCELLED = 'cancelled',
  RETURNED = 'returned',
}

export enum OrderPriorityService {
  LOW = 'low',
  NORMAL = 'normal',
  HIGH = 'high',
  URGENT = 'urgent',
}

export interface CreateOrderInput {
  storeId: string | bigint;
  customerId?: string | bigint;
  userId?: string | bigint;
  status?: OrderStatusService;
  priority?: OrderPriorityService;
  useTax?: boolean;
  taxRate?: number;
  orderDate?: Date;
  expectedDeliveryDate?: Date;
  preferredDeliveryLocation?: string;
  items: Array<{
    productId: string | bigint;
    productName: string;
    quantity: number;
    unitPrice: number;
    lineTotal: number;
    taxAmount?: number;
  }>;
  createdBy?: string | bigint;
}

export interface UpdateOrderInput {
  id: string | bigint;
  status?: OrderStatusService;
  priority?: OrderPriorityService;
  expectedDeliveryDate?: Date;
  preferredDeliveryLocation?: string;
  updatedBy?: string | bigint;
}

async function generateNextOrderNumber(tx: any): Promise<string> {
  const result = await tx.query(`
    SELECT COALESCE(MAX(CAST(SUBSTRING(order_number FROM 6) AS INTEGER)), 0) + 1 as next_number
    FROM orders 
    WHERE order_number ~ '^ORD-[0-9]+$'
  `);
  
  const nextNumber = result[0]?.next_number || 1;
  return `ORD-${nextNumber.toString().padStart(6, '0')}`;
}

function computeOrderTotals(
  items: Array<{ quantity: number; unitPrice: number; taxAmount?: number }>,
  useTax: boolean,
  taxRate: number
) {
  const subtotal = items.reduce((sum, item) => sum + (item.quantity * item.unitPrice), 0);
  const taxAmount = useTax ? subtotal * (taxRate / 100) : 0;
  const total = subtotal + taxAmount;
  
  return { subtotal, taxAmount, total };
}

export async function createOrder(
  input: CreateOrderInput,
  db: DataSource
): Promise<Order> {
  return db.manager.transaction(async (tx) => {
    const orderNumber = await generateNextOrderNumber(tx);
    const { subtotal, taxAmount, total } = computeOrderTotals(
      input.items,
      input.useTax || false,
      input.taxRate || 0
    );

    const order = new Order();
    order.orderNumber = orderNumber;
    order.status = (input.status as any) || 'pending';
    order.priority = (input.priority as any) || 'normal';
    order.storeId = input.storeId.toString();
    order.customerId = input.customerId?.toString();
    order.userId = input.userId?.toString();
    order.useTax = input.useTax || false;
    order.taxRate = input.taxRate || 0;
    order.orderDate = input.orderDate || new Date();
    order.expectedDeliveryDate = input.expectedDeliveryDate;
    order.preferredDeliveryLocation = input.preferredDeliveryLocation;
    order.subtotal = subtotal;
    order.taxAmount = taxAmount;
    order.total = total;
    order.createdBy = input.createdBy?.toString();

    const savedOrder = await tx.save(Order, order);

    // Create order items
    for (const item of input.items) {
      const orderItem = new OrderItem();
      orderItem.orderId = savedOrder.id;
      orderItem.productId = item.productId.toString();
      orderItem.productName = item.productName;
      orderItem.quantity = item.quantity;
      orderItem.unitPrice = item.unitPrice;
      orderItem.lineTotal = item.lineTotal;
      orderItem.taxAmount = item.taxAmount || 0;
      orderItem.createdBy = input.createdBy?.toString();

      await tx.save(OrderItem, orderItem);
    }

    return savedOrder;
  });
}

export async function updateOrder(
  input: UpdateOrderInput,
  db: DataSource
): Promise<Order> {
  const orderRepository = db.getRepository(Order);
  
  const order = await orderRepository.findOne({
    where: { id: input.id.toString(), isDeleted: false },
  });

  if (!order) {
    throw new Error(`Order with ID ${input.id} not found`);
  }

  if (input.status) order.status = input.status as any;
  if (input.priority) order.priority = input.priority as any;
  if (input.expectedDeliveryDate) order.expectedDeliveryDate = input.expectedDeliveryDate;
  if (input.preferredDeliveryLocation) order.preferredDeliveryLocation = input.preferredDeliveryLocation;
  if (input.updatedBy) order.updatedBy = input.updatedBy.toString();

  return orderRepository.save(order);
}

export async function checkOrderUnderwayByPhone(
  phone: string,
  db: DataSource
): Promise<Order | null> {
  const orderRepository = db.getRepository(Order);
  
  const underwayStatuses = [
    OrderStatusService.PENDING,
    OrderStatusService.CONFIRMED,
    OrderStatusService.PROCESSING,
    OrderStatusService.SHIPPED
  ];

  return orderRepository
    .createQueryBuilder('order')
    .leftJoinAndSelect('order.customer', 'customer')
    .where('customer.phone = :phone', { phone })
    .andWhere('order.status IN (:...statuses)', { statuses: underwayStatuses })
    .andWhere('order.isDeleted = false')
    .orderBy('order.createdAt', 'DESC')
    .getOne();
}
