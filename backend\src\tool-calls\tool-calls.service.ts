import { Injectable, NotFoundException } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { ToolCall } from './tool-call.entity';

@Injectable()
export class ToolCallsService {
  constructor(
    @InjectRepository(ToolCall)
    private toolCallsRepository: Repository<ToolCall>,
  ) {}

  async findAll(): Promise<ToolCall[]> {
    return this.toolCallsRepository.find({
      where: { isDeleted: false },
      order: { createdAt: 'DESC' },
    });
  }

  async findOne(id: string): Promise<ToolCall> {
    const toolCall = await this.toolCallsRepository.findOne({
      where: { id, isDeleted: false },
    });
    
    if (!toolCall) {
      throw new NotFoundException(`ToolCall with ID ${id} not found`);
    }
    
    return toolCall;
  }

  async findByConversationId(conversationId: string): Promise<ToolCall[]> {
    return this.toolCallsRepository.find({
      where: { conversationId, isDeleted: false },
      order: { createdAt: 'ASC' },
    });
  }

  async create(createToolCallDto: Partial<ToolCall> & { userId?: string | number }): Promise<ToolCall> {
    // Extract userId and map it to the correct fields
    const { userId, ...toolCallData } = createToolCallDto;

    // Create the tool call with proper field mapping
    const toolCall = this.toolCallsRepository.create({
      ...toolCallData,
      createdBy: userId?.toString(),
    });

    return this.toolCallsRepository.save(toolCall);
  }

  async update(id: string, updateToolCallDto: Partial<ToolCall>): Promise<ToolCall> {
    const toolCall = await this.findOne(id);
    Object.assign(toolCall, updateToolCallDto);
    return this.toolCallsRepository.save(toolCall);
  }

  async remove(id: string): Promise<void> {
    const toolCall = await this.findOne(id);
    toolCall.isDeleted = true;
    await this.toolCallsRepository.save(toolCall);
  }
}
