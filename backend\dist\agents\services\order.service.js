"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.OrderPriorityService = exports.OrderStatusService = void 0;
exports.createOrder = createOrder;
exports.updateOrder = updateOrder;
exports.checkOrderUnderwayByPhone = checkOrderUnderwayByPhone;
const order_entity_1 = require("../../orders/order.entity");
const order_item_entity_1 = require("../../orders/order-item.entity");
var OrderStatusService;
(function (OrderStatusService) {
    OrderStatusService["DRAFT"] = "draft";
    OrderStatusService["PENDING"] = "pending";
    OrderStatusService["CONFIRMED"] = "confirmed";
    OrderStatusService["PROCESSING"] = "processing";
    OrderStatusService["SHIPPED"] = "shipped";
    OrderStatusService["DELIVERED"] = "delivered";
    OrderStatusService["CANCELLED"] = "cancelled";
    OrderStatusService["RETURNED"] = "returned";
})(OrderStatusService || (exports.OrderStatusService = OrderStatusService = {}));
var OrderPriorityService;
(function (OrderPriorityService) {
    OrderPriorityService["LOW"] = "low";
    OrderPriorityService["NORMAL"] = "normal";
    OrderPriorityService["HIGH"] = "high";
    OrderPriorityService["URGENT"] = "urgent";
})(OrderPriorityService || (exports.OrderPriorityService = OrderPriorityService = {}));
async function generateNextOrderNumber(tx) {
    const result = await tx.query(`
    SELECT COALESCE(MAX(CAST(SUBSTRING(order_number FROM 6) AS INTEGER)), 0) + 1 as next_number
    FROM orders 
    WHERE order_number ~ '^ORD-[0-9]+$'
  `);
    const nextNumber = result[0]?.next_number || 1;
    return `ORD-${nextNumber.toString().padStart(6, '0')}`;
}
function computeOrderTotals(items, useTax, taxRate) {
    const subtotal = items.reduce((sum, item) => sum + (item.quantity * item.unitPrice), 0);
    const taxAmount = useTax ? subtotal * (taxRate / 100) : 0;
    const total = subtotal + taxAmount;
    return { subtotal, taxAmount, total };
}
async function createOrder(input, db) {
    return db.manager.transaction(async (tx) => {
        const orderNumber = await generateNextOrderNumber(tx);
        const { subtotal, taxAmount, total } = computeOrderTotals(input.items, input.useTax || false, input.taxRate || 0);
        const order = new order_entity_1.Order();
        order.orderNumber = orderNumber;
        order.status = input.status || 'pending';
        order.priority = input.priority || 'normal';
        order.storeId = input.storeId.toString();
        order.customerId = input.customerId?.toString();
        order.userId = input.userId?.toString();
        order.useTax = input.useTax || false;
        order.taxRate = input.taxRate || 0;
        order.orderDate = input.orderDate || new Date();
        order.expectedDeliveryDate = input.expectedDeliveryDate;
        order.preferredDeliveryLocation = input.preferredDeliveryLocation;
        order.subtotal = subtotal;
        order.taxAmount = taxAmount;
        order.total = total;
        order.createdBy = input.createdBy?.toString();
        const savedOrder = await tx.save(order_entity_1.Order, order);
        for (const item of input.items) {
            const orderItem = new order_item_entity_1.OrderItem();
            orderItem.orderId = savedOrder.id;
            orderItem.productId = item.productId.toString();
            orderItem.productName = item.productName;
            orderItem.quantity = item.quantity;
            orderItem.unitPrice = item.unitPrice;
            orderItem.lineTotal = item.lineTotal;
            orderItem.taxAmount = item.taxAmount || 0;
            orderItem.createdBy = input.createdBy?.toString();
            await tx.save(order_item_entity_1.OrderItem, orderItem);
        }
        return savedOrder;
    });
}
async function updateOrder(input, db) {
    const orderRepository = db.getRepository(order_entity_1.Order);
    const order = await orderRepository.findOne({
        where: { id: input.id.toString(), isDeleted: false },
    });
    if (!order) {
        throw new Error(`Order with ID ${input.id} not found`);
    }
    if (input.status)
        order.status = input.status;
    if (input.priority)
        order.priority = input.priority;
    if (input.expectedDeliveryDate)
        order.expectedDeliveryDate = input.expectedDeliveryDate;
    if (input.preferredDeliveryLocation)
        order.preferredDeliveryLocation = input.preferredDeliveryLocation;
    if (input.updatedBy)
        order.updatedBy = input.updatedBy.toString();
    return orderRepository.save(order);
}
async function checkOrderUnderwayByPhone(phone, db) {
    const orderRepository = db.getRepository(order_entity_1.Order);
    const underwayStatuses = [
        OrderStatusService.PENDING,
        OrderStatusService.CONFIRMED,
        OrderStatusService.PROCESSING,
        OrderStatusService.SHIPPED
    ];
    return orderRepository
        .createQueryBuilder('order')
        .leftJoinAndSelect('order.customer', 'customer')
        .where('customer.phone = :phone', { phone })
        .andWhere('order.status IN (:...statuses)', { statuses: underwayStatuses })
        .andWhere('order.isDeleted = false')
        .orderBy('order.createdAt', 'DESC')
        .getOne();
}
//# sourceMappingURL=order.service.js.map