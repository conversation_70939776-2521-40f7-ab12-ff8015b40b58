import { DataSource, EntityManager } from 'typeorm';
import { Agent, setLlmProviderFromEnvironmentVariables, type Message as AgentMessage } from '../../../../ai-agent/dist';
import { buildConversationTools } from './agentTools/agent-conversation-tools';
import { Agent as AgentEntity } from '../agent.entity';

export async function createDefaultAgent(db: DataSource | EntityManager, userId: string) {
	// Minimal agent per schema: only name
	// We scope naming to user to avoid collisions for human readability
	const name = `Default Agent for User ${userId.toString()}`;
	const agentRepository = db.getRepository(AgentEntity);
	return agentRepository.save({ name });
}


// ===== Agent System (extracted from conversation-agent.service) =====

type BigIntLike = bigint | string | number;

export enum ConversationNotification {
	AgentIsThinking = 'AgentIsThinking',
	AgentIsGeneratingResponse = 'AgentIsGeneratingResponse',
	None = 'None',
}

export interface AgentRuntimeCallbacks {
	setNotification?: (conversationUuid: string, notification: ConversationNotification) => Promise<void> | void;
}

export interface ProcessUserMessageInput {
	conversationUuid: string;
	userMessage: string;
	ownerUserId: BigIntLike;
	agentId: string;
}

export interface GenerateMessageInput {
	conversationUuid: string;
	agentId: string;
	ownerUserId: BigIntLike;
}

export function toBigInt(id: BigIntLike): bigint {
	if (typeof id === 'bigint') return id;
	if (typeof id === 'number') return BigInt(id);
	return BigInt(id);
}

export function mapLanguageCodeToName(languageCode: string): string {
	const languageMap: Record<string, string> = {
		'en': 'English',
		'es': 'Spanish',
		'fr': 'French',
		'de': 'German',
		'it': 'Italian',
		'pt': 'Portuguese',
		'ru': 'Russian',
		'zh': 'Chinese',
		'ja': 'Japanese',
		'ko': 'Korean',
		'ar': 'Arabic',
		'hi': 'Hindi',
		'bn': 'Bengali',
		'pa': 'Punjabi',
		'ur': 'Urdu',
		'fa': 'Persian',
		'tr': 'Turkish',
		'nl': 'Dutch',
		'sv': 'Swedish',
		'no': 'Norwegian',
		'da': 'Danish',
		'fi': 'Finnish',
		'pl': 'Polish',
		'cs': 'Czech',
		'sk': 'Slovak',
		'hu': 'Hungarian',
		'ro': 'Romanian',
		'bg': 'Bulgarian',
		'hr': 'Croatian',
		'sl': 'Slovenian',
		'et': 'Estonian',
		'lv': 'Latvian',
		'lt': 'Lithuanian',
		'el': 'Greek',
		'he': 'Hebrew',
		'th': 'Thai',
		'vi': 'Vietnamese',
		'id': 'Indonesian',
		'ms': 'Malay',
		'ta': 'Tamil',
		'te': 'Telugu',
		'ml': 'Malayalam',
		'kn': 'Kannada',
		'mr': 'Marathi',
		'gu': 'Gujarati',
		'or': 'Oriya',
		'as': 'Assamese',
		'ne': 'Nepali',
		'si': 'Sinhala',
		'km': 'Khmer',
		'lo': 'Lao',
		'my': 'Burmese',
		'ka': 'Georgian',
		'hy': 'Armenian',
		'az': 'Azerbaijani',
		'kk': 'Kazakh',
		'uz': 'Uzbek',
		'tk': 'Turkmen',
		'ky': 'Kyrgyz',
		'mn': 'Mongolian',
		'bo': 'Tibetan',
		'ug': 'Uyghur'
	};

	return languageMap[languageCode] || 'English'; // fallback to English if unknown
}



export function buildConversationAgent(
	db: DataSource,
	conversationUuid: string,
	storeCurrency: string,
	preferredLanguage: string = 'English'
): Agent {
	const tools = buildConversationTools(db, conversationUuid);
	const frontendUrl = process.env.FRONTEND_URL || 'http://localhost:3000';
	// Convert language code to full language name if needed
	const languageName = preferredLanguage.length === 2 ? mapLanguageCodeToName(preferredLanguage) : preferredLanguage;

	return new Agent(
		'Conversation Agent',
		[
			'# Role',
			'You are a helpful and friendly sales assistant bot. You will be talking only to customers. Your main responsibilities are:',
			'1. Greet customers warmly and introduce yourself',
			'2. Explain your capabilities and how you can help them',
			'3. Present any previous orders that the client has made',
			'4. Present product information in an engaging way',
			'5. Help customers make purchasing decisions',
			'6. Process orders efficiently',
			'7. You only talk to customers', 
			'',
			'When interacting with customers:',
			'- Be professional but conversational',
			'- Focus on understanding client needs',
			'- Provide clear and accurate product information',
			'- Be transparent about pricing and delivery',
			'- Handle objections professionally',
			'- Always maintain a helpful and positive tone',
			'',
			'# Additional Notes',
			'Remember to:',
			'- Ask clarifying questions when needed',
			'- Provide specific product recommendations based on client needs',
			'- Explain the ordering process clearly',
			'- Confirm order details before processing',
			'- Thank clients for their business',
			'- When you list products, use the following format:',
			'    - <Product Name>',
			'    - <Product Description>',
			'    - <Product Price>',
			'- When you list products, only list a maximum of 20 products at a time',
			'- Only show the product image url when specifically asked',
			'- For more detailed information and to purchase, please visit our store. Use the getStoreUrl tool to get the correct store URL.',
			'',
			'# Ordering Process',
			'1. Client provides product details',
			'2. Bot confirms order details and asks for <customer_name> and <location_name> and <phone_number> if the customer has not yet mentioned them',
			'3. Client provides <client_name> and <location_name> and <phone number>',
			'4. If order is successful the Bot processes order and sends confirmation with receipt and delivery details to client and message for status of order otherwise will tell the customer there was issue.',
			'5. Bot asks if there is anything else the customer wants to order',
			'',
			'# Order Tracking Process',
			'- Client can track the order by asking or when they place an order',
			'- Bot will send a message with the status of the order (Use random values for now, we are in testing phase)',
			'- Format for order status message:',
			'    - Order ID: <order_number>',
			`    - Order Tracking Url: ${frontendUrl}/track/<order_number> example: ${frontendUrl}/track/ORD-20250821-6167`,
			'    - Order Status: <order_status>',
			'    - Order Delivery Date: <order_delivery_date>',
			'    - Order Delivery Time: <order_delivery_time>',
			'    - Order Delivery Location: <order_delivery_location>',
			'    - Order Delivery Phone Number: <order_delivery_phone_number>',
			'',
			'# Important Notes',
			'- <customer_name>, <customer_phone> and <location_name> are all required when doing customer orders',
			'- Always respond in a friendly and helpful manner',
			'- Remember to keep the conversation engaging and interesting',
			'- The <location_name> does not need to be detailed',
			'- The primary language of communication is ' + languageName,
			'- Always respond in ' + languageName,
			'- Use emojis to make the conversation more engaging',
			'- Price currency is ' + storeCurrency,
			'- We dont use emails to send receipts or track orders, we use messages',
			'- Lookup older messages for user information like phone or name or email',
			'',
			'# Technical Implementation Details',
			'You are provided the full conversation history as context, including previous tool calls and their results. You can see what tools were previously executed, their inputs, outputs, success status, and execution times. Use this information to make informed decisions about what tools to call next.',
			'When the user asks for products or a product list, answer based on the product list provided in your context. Do not fabricate product data.',
			'Avoid fabricating customer data. If customer details are missing for ordering, ask the user for customer name and phone number and optionally email, then place the order using placeOrder.',
			'NEVER reveal or mention tool names, function names, implementation details, phases, or any backend/system internals to the user. Do not include phrases like "Phase 1", "Phase 2", "Call:", or "Output:" in any user-facing message. Present only a natural, human-friendly response.',
			'If a tool returns an error, summarize the outcome naturally without exposing the tool or error text. Keep answers concise and helpful. Ask clarifying questions if needed.',
			`The customer can check his order using the link ${frontendUrl}/track/<order_number> example: ${frontendUrl}/track/ORD-20250821-0157. Use the getStoreUrl tool to get the correct store URL.`
		].join('\n'),
		tools,
		undefined // handoffDescription
	);
}

// Helper functions to build context messages
export function buildProductContextMessage(
	products: Array<{ id: bigint; name: string; description: string | null; price: number; sku: string | null }>,
	storeCurrency: string
): AgentMessage | null {
	if (!products || products.length === 0) {
		return {
			role: 'system',
			content: '# Available Products\n\nThe product list is currently empty. No products are available in the store at this time.'
		};
	}

	let productContext = '# Available Products\n';
	productContext += 'Here are the products available in our store:\n';
	products.forEach((product, index) => {
		productContext += `${index + 1}. ${product.name}\n`;
		productContext += `   Product ID: ${product.id}\n`;
		if (product.description) {
			productContext += `   Description: ${product.description}\n`;
		}
		productContext += `   Price: ${storeCurrency} ${product.price}\n`;
		if (product.sku) {
			productContext += `   SKU: ${product.sku}\n`;
		}
		productContext += '\n';
	});

	return {
		role: 'system',
		content: productContext.trim()
	};
}

export function buildCustomerContextMessage(
	customer: { name: string | null; email: string | null; phone: string | null; address: string | null } | null
): AgentMessage | null {
	if (!customer) {
		return {
			role: 'system',
			content: '# Current Customer Information\n\nNo customer information is available for this conversation. You will need to collect customer details when processing orders.'
		};
	}

	let customerContext = '# Current Customer Information\n';
	customerContext += 'This conversation is with:\n';
	if (customer.name) {
		customerContext += `- Name: ${customer.name}\n`;
	}
	if (customer.email) {
		customerContext += `- Email: ${customer.email}\n`;
	}
	if (customer.phone) {
		customerContext += `- Phone: ${customer.phone}\n`;
	}
	if (customer.address) {
		customerContext += `- Address: ${customer.address}\n`;
	}

	return {
		role: 'system',
		content: customerContext.trim()
	};
}

export async function ensureLLMProviderConfigured() {
	setLlmProviderFromEnvironmentVariables();
}





