import { DataSource } from 'typeorm';
import { Order } from '../../orders/order.entity';
export declare enum OrderStatusService {
    DRAFT = "draft",
    PENDING = "pending",
    CONFIRMED = "confirmed",
    PROCESSING = "processing",
    SHIPPED = "shipped",
    DELIVERED = "delivered",
    CANCELLED = "cancelled",
    RETURNED = "returned"
}
export declare enum OrderPriorityService {
    LOW = "low",
    NORMAL = "normal",
    HIGH = "high",
    URGENT = "urgent"
}
export interface CreateOrderInput {
    storeId: string | bigint;
    customerId?: string | bigint;
    userId?: string | bigint;
    status?: OrderStatusService;
    priority?: OrderPriorityService;
    useTax?: boolean;
    taxRate?: number;
    orderDate?: Date;
    expectedDeliveryDate?: Date;
    preferredDeliveryLocation?: string;
    items: Array<{
        productId: string | bigint;
        productName: string;
        quantity: number;
        unitPrice: number;
        lineTotal: number;
        taxAmount?: number;
    }>;
    createdBy?: string | bigint;
}
export interface UpdateOrderInput {
    id: string | bigint;
    status?: OrderStatusService;
    priority?: OrderPriorityService;
    expectedDeliveryDate?: Date;
    preferredDeliveryLocation?: string;
    updatedBy?: string | bigint;
}
export declare function createOrder(input: CreateOrderInput, db: DataSource): Promise<Order>;
export declare function updateOrder(input: UpdateOrderInput, db: DataSource): Promise<Order>;
export declare function checkOrderUnderwayByPhone(phone: string, db: DataSource): Promise<Order | null>;
